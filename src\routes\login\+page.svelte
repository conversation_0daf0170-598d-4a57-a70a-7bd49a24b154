<script lang="ts">
	import { goto } from '$app/navigation';
	import { auth } from '$lib/stores/auth';
	import { onMount } from 'svelte';

	let username = '';
	let password = '';
	let error = '';
	let loading = false;

	// Autofocus username field on mount
	onMount(() => {
		const usernameInput = document.getElementById('username-input');
		if (usernameInput) usernameInput.focus();
	});

	const login = async (e?: SubmitEvent) => {
		if (e) e.preventDefault();

		if (!username || !password) {
			error = 'Please enter both username and password';
			return;
		}

		loading = true;
		error = '';

		try {
			const res = await fetch('/api/auth/login', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ username, password })
			});

			const data = await res.json();

			if (res.ok) {
				auth.setAuth(data.token, data.username);
				goto('/chat', { replaceState: true });
			} else {
				error = data.error || 'Invalid credentials';
			}
		} catch (err) {
			console.error(err);
			error = 'An error occurred while logging in';
		} finally {
			loading = false;
		}
	};

	const handleKeydown = (e: KeyboardEvent) => {
		if (e.key === 'Enter') {
			login();
		}
	};
</script>

<div class="min-h-screen bg-slate-50">
	<div class="container mx-auto px-4 py-8">
		<nav class="mb-8 flex items-center justify-between">
			<a href="/" class="font-mono text-xl text-black">konekt</a>
			<div class="flex items-center space-x-6">
				<a href="/" class="font-mono text-sm text-black hover:text-gray-600">home</a>
				<a href="/login" class="font-mono text-sm text-black hover:text-gray-600">login</a>
				<a href="/register" class="font-mono text-sm text-black hover:text-gray-600">register</a>
			</div>
		</nav>

		<div class="mx-auto max-w-md">
			<div class="rounded-lg bg-white p-8 shadow-lg">
				<h1 class="mb-6 font-mono text-2xl text-black">login</h1>

				<form on:submit={login} class="space-y-4">
					<div>
						<input
							id="username-input"
							bind:value={username}
							placeholder="username"
							autocomplete="username"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
							disabled={loading}
						/>
					</div>
					<div>
						<input
							bind:value={password}
							type="password"
							placeholder="password"
							autocomplete="current-password"
							on:keydown={handleKeydown}
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
							disabled={loading}
						/>
					</div>
					{#if error}
						<p class="text-sm text-red-500">{error}</p>
					{/if}
					<button
						type="submit"
						class="w-full rounded-md bg-black px-4 py-2 font-mono text-sm text-white transition-colors hover:bg-gray-800 disabled:opacity-50"
						disabled={loading}
					>
						{loading ? 'logging in...' : 'login'}
					</button>
				</form>
			</div>
		</div>
	</div>
</div>
