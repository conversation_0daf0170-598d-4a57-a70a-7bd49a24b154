import bcrypt from 'bcryptjs';
import type { RequestHand<PERSON> } from './$types';
import User from '$lib/models/User';
import { connect } from '$lib/db';

export const POST: RequestHandler = async ({ request }) => {
  await connect();
  const { username, password } = await request.json();
  const hashedPassword = await bcrypt.hash(password, 10);

  try {
    await User.create({ username, password: hashedPassword });
    return new Response(JSON.stringify({ message: 'User created' }), { status: 201 });
  } catch {
    return new Response(JSON.stringify({ error: 'Username already exists' }), { status: 400 });
  }
};
