import { WebSocketServer, WebSocket } from 'ws'; // Import WebSocket type
import { parse } from 'url';

// Keep track of connections AND mapping from username to WebSocket SET
const userConnections = new Map<string, Set<WebSocket>>();
const wsToUsername = new Map<WebSocket, string>(); // Helper map

export function startWebSocketServer() {
	const wss = new WebSocketServer({ port: 6969 });
	console.log('WebSocket server started on port 6969');

	wss.on('connection', (ws, req) => {
		const { query } = parse(req.url!, true);
		const username = query.username as string;

		if (!username) {
			ws.close(4001, 'Username required');
			return;
		}

		// Register connection
		if (!userConnections.has(username)) {
			userConnections.set(username, new Set());
		}
		const userWsSet = userConnections.get(username)!;
		userWsSet.add(ws);
		wsToUsername.set(ws, username); // Map WebSocket to username

		console.log(`New connection for ${username} (Total: ${userWsSet.size})`);

		// Broadcaster function
		const broadcastToUser = (targetUsername: string, message: any) => {
			const targetWsSet = userConnections.get(targetUsername);
			if (targetWsSet) {
				targetWsSet.forEach((client) => {
					if (client.readyState === WebSocket.OPEN) {
						client.send(JSON.stringify(message));
					}
				});
			}
		};

		const broadcastToGroup = (memberUsernames: string[], message: any, senderUsername: string) => {
			memberUsernames.forEach((memberUsername) => {
				if (memberUsername !== senderUsername) {
					// Don't send back to sender
					broadcastToUser(memberUsername, message);
				}
			});
		};

		ws.on('message', (data) => {
			try {
				const message = JSON.parse(data.toString());
				const senderUsername = wsToUsername.get(ws);

				if (!senderUsername) return; // Should not happen

				console.log(`Message from ${senderUsername}:`, message.type);

				switch (message.type) {
					// Existing call signaling
					case 'call-request':
					case 'ice-candidate':
					case 'answer':
					case 'call-rejected':
						broadcastToUser(message.to, { ...message, from: senderUsername });
						break;

					// Typing indicators
					case 'typing':
					case 'stopped-typing':
						if (message.isGroup) {
							broadcastToGroup(
								message.members,
								{ ...message, from: senderUsername },
								senderUsername
							);
						} else {
							broadcastToUser(message.to, { ...message, from: senderUsername });
						}
						break;

					// Deletion broadcasts (client-initiated)
					case 'broadcast-delete-message':
						if (message.isGroup) {
							broadcastToGroup(
								message.members,
								{
									type: 'message-deleted',
									messageId: message.messageId,
									groupId: message.conversationTarget // Group ID
								},
								senderUsername
							);
						} else {
							broadcastToUser(message.conversationTarget, {
								// Target username
								type: 'message-deleted',
								messageId: message.messageId,
								from: senderUsername // Who deleted it (sender of original message)
							});
						}
						break;

					case 'broadcast-delete-group':
						broadcastToGroup(
							message.memberUsernames,
							{
								type: 'group-deleted',
								groupId: message.groupId
							},
							senderUsername
						); // Notify all members
						break;

					default:
						console.log(`Unknown message type from ${senderUsername}: ${message.type}`);
				}
			} catch (error) {
				console.error('Failed to process message:', error);
			}
		});

		const cleanup = () => {
			const username = wsToUsername.get(ws);
			if (username) {
				const userWsSet = userConnections.get(username);
				if (userWsSet) {
					userWsSet.delete(ws);
					console.log(`Connection closed for ${username} (Remaining: ${userWsSet.size})`);
					if (userWsSet.size === 0) {
						userConnections.delete(username);
						console.log(`User ${username} fully disconnected.`);
					}
				}
				wsToUsername.delete(ws); // Clean up helper map
			}
		};

		ws.on('close', (code, reason) => {
			console.log(`WebSocket closing for ${wsToUsername.get(ws)}: ${code} ${reason.toString()}`);
			cleanup();
		});

		ws.on('error', (error) => {
			console.error(`WebSocket error for ${wsToUsername.get(ws)}:`, error);
			cleanup(); // Ensure cleanup on error
		});
	});

	// Optional: Clean up maps on server shutdown (if needed)
	process.on('SIGTERM', () => {
		console.log('Closing WebSocket server...');
		wss.close();
		userConnections.clear();
		wsToUsername.clear();
	});

	return wss;
}
