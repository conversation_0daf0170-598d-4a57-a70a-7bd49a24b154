import crypto from 'crypto';

// Use an environment variable for the encryption key
const ENCRYPTION_KEY = crypto
	.createHash('sha256')
	.update(process.env.ENCRYPTION_KEY || 'your-fallback-secret-key')
	.digest();

const IV_LENGTH = 16;

export function encrypt(text: string): string {
	try {
		const iv = crypto.randomBytes(IV_LENGTH);
		const cipher = crypto.createCipheriv('aes-256-cbc', ENCRYPTION_KEY, iv);
		let encrypted = cipher.update(text, 'utf8', 'hex');
		encrypted += cipher.final('hex');
		return `encrypted:${iv.toString('hex')}:${encrypted}`; // Add prefix to identify encrypted messages
	} catch (error) {
		console.error('Encryption error:', error);
		throw new Error('Encryption failed');
	}
}

export function decrypt(text: string): string {
	try {
		// Check if the text is encrypted with our format
		if (!text.startsWith('encrypted:')) {
			return text; // Return original text if it's not encrypted
		}

		const [prefix, ivHex, encryptedHex] = text.split(':');
		if (!ivHex || !encryptedHex) {
			console.error('Invalid encrypted format');
			return '[Invalid encrypted format]';
		}

		const iv = Buffer.from(ivHex, 'hex');
		const decipher = crypto.createDecipheriv('aes-256-cbc', ENCRYPTION_KEY, iv);
		let decrypted = decipher.update(encryptedHex, 'hex', 'utf8');
		decrypted += decipher.final('utf8');
		return decrypted;
	} catch (error) {
		console.error('Decryption error:', error);
		return '[Decryption failed]'; // Return a placeholder for failed decryption
	}
}
