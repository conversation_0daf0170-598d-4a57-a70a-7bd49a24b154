import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';

export const GET: RequestHandler = async ({ cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		const decoded: any = jwt.verify(token, 'your-secret-key');
		const user = await User.findById(decoded.userId);

		if (!user) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		return json({
			username: user.username,
			profilePicture: user.profilePicture
		});
	} catch (err) {
		console.error('Error fetching profile:', err);
		return json({ error: 'Failed to fetch profile' }, { status: 500 });
	}
};

export const PUT: RequestHandler = async ({ request, cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		const decoded: any = jwt.verify(token, 'your-secret-key');
		const formData = await request.formData();
		const updates: any = {};

		if (formData.has('username')) {
			updates.username = formData.get('username');
		}

		if (formData.has('profilePicture')) {
			// Handle file upload here
			// You'll need to implement file storage (e.g., using a service like S3)
			const file = formData.get('profilePicture') as File;
			// updates.profilePicture = await uploadFile(file);
		}

		const user = await User.findByIdAndUpdate(decoded.userId, updates, { new: true });

		return json({ success: true, user });
	} catch (err) {
		console.error('Error updating profile:', err);
		return json({ error: 'Failed to update profile' }, { status: 500 });
	}
};
