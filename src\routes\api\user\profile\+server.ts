import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import r2Storage from '$lib/services/r2Storage';
import { connect } from '$lib/db';

export const GET: RequestHandler = async ({ cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		await connect();
		const decoded: any = jwt.verify(token, 'your-secret-key');
		const user = await User.findById(decoded.userId);

		if (!user) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		return json({
			username: user.username,
			profilePicture: user.profilePicture
		});
	} catch (err) {
		console.error('Error fetching profile:', err);
		return json({ error: 'Failed to fetch profile' }, { status: 500 });
	}
};

export const PUT: RequestHandler = async ({ request, cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		await connect();
		const decoded: any = jwt.verify(token, 'your-secret-key');
		const formData = await request.formData();
		const updates: any = {};

		// Get current user to handle old profile picture deletion
		const currentUser = await User.findById(decoded.userId);
		if (!currentUser) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		if (formData.has('username')) {
			const newUsername = formData.get('username') as string;
			if (newUsername && newUsername.trim()) {
				updates.username = newUsername.trim();
			}
		}

		if (formData.has('profilePicture')) {
			const file = formData.get('profilePicture') as File;

			if (file && file.size > 0) {
				// Validate file
				const validation = r2Storage.validateFile(file, 'avatar');
				if (!validation.valid) {
					return json({ error: validation.error }, { status: 400 });
				}

				// Delete old profile picture if exists
				if (currentUser.profilePicture) {
					try {
						// Extract key from URL
						const oldKey = currentUser.profilePicture.split('/').pop();
						if (oldKey) {
							await r2Storage.deleteFile(`avatars/${decoded.userId}/${oldKey}`);
						}
					} catch (deleteError) {
						console.warn('Failed to delete old profile picture:', deleteError);
					}
				}

				// Upload new profile picture
				const key = r2Storage.generateFileKey(decoded.userId, 'avatar', file.name);
				const uploadResult = await r2Storage.uploadFile(file, key, file.type, {
					userId: decoded.userId,
					uploadType: 'avatar'
				});

				updates.profilePicture = uploadResult.url;
			}
		}

		const user = await User.findByIdAndUpdate(decoded.userId, updates, { new: true });

		return json({
			success: true,
			user: {
				username: user?.username,
				profilePicture: user?.profilePicture
			}
		});
	} catch (err) {
		console.error('Error updating profile:', err);
		return json({ error: 'Failed to update profile' }, { status: 500 });
	}
};
