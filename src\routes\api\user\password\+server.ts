import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';

export const PUT: RequestHandler = async ({ request, cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		const decoded: any = jwt.verify(token, 'your-secret-key');
		const { currentPassword, newPassword } = await request.json();

		const user = await User.findById(decoded.userId);
		if (!user) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
		if (!isPasswordValid) {
			return json({ error: 'Current password is incorrect' }, { status: 400 });
		}

		const hashedPassword = await bcrypt.hash(newPassword, 10);
		user.password = hashedPassword;
		await user.save();

		return json({ success: true });
	} catch (err) {
		console.error('Error updating password:', err);
		return json({ error: 'Failed to update password' }, { status: 500 });
	}
};
