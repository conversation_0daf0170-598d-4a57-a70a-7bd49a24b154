import mongoose, { Schema, Document } from 'mongoose';
import Chat from './Chat';

interface IUser extends Document {
	username: string;
	password: string;
	profilePicture?: string;
}

const UserSchema = new Schema<IUser>({
	username: { type: String, required: true, unique: true },
	password: { type: String, required: true },
	profilePicture: { type: String }
});

// Delete user's messages when user is deleted
UserSchema.pre('remove', async function (next) {
	const userId = this._id;
	try {
		await Chat.deleteMany({
			$or: [{ sender: userId }, { receiver: userId }]
		});
		next();
	} catch (error) {
		next(error as Error);
	}
});

const User = mongoose.models.User || mongoose.model<IUser>('User', UserSchema);

export default User;
