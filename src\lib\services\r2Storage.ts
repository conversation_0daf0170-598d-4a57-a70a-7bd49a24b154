import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { dev } from '$app/environment';

// R2 Configuration
const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID || 'demo_account_id';
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID || 'demo_access_key';
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY || 'demo_secret_key';
const R2_BUCKET_NAME = process.env.R2_BUCKET_NAME || 'konekt-dev';
const R2_PUBLIC_URL = process.env.R2_PUBLIC_URL || 'https://demo-bucket.demo-account.r2.cloudflarestorage.com';

// Create S3 client configured for Cloudflare R2
const r2Client = new S3Client({
	region: 'auto',
	endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
	credentials: {
		accessKeyId: R2_ACCESS_KEY_ID!,
		secretAccessKey: R2_SECRET_ACCESS_KEY!,
	},
});

export interface UploadResult {
	key: string;
	url: string;
	size: number;
	contentType: string;
}

export class R2StorageService {
	private static instance: R2StorageService;

	static getInstance(): R2StorageService {
		if (!R2StorageService.instance) {
			R2StorageService.instance = new R2StorageService();
		}
		return R2StorageService.instance;
	}

	/**
	 * Upload a file to R2 storage
	 */
	async uploadFile(
		file: File | Buffer,
		key: string,
		contentType: string,
		metadata?: Record<string, string>
	): Promise<UploadResult> {
		// Check if R2 is properly configured
		if (R2_ACCOUNT_ID === 'demo_account_id' || R2_ACCESS_KEY_ID === 'demo_access_key') {
			console.warn('R2 not configured - using mock upload');
			// Return a mock result for development
			const buffer = file instanceof File ? Buffer.from(await file.arrayBuffer()) : file;
			return {
				key,
				url: `https://via.placeholder.com/150x150.png?text=Mock+File`, // Placeholder image
				size: buffer.length,
				contentType,
			};
		}

		try {
			const buffer = file instanceof File ? Buffer.from(await file.arrayBuffer()) : file;

			const command = new PutObjectCommand({
				Bucket: R2_BUCKET_NAME,
				Key: key,
				Body: buffer,
				ContentType: contentType,
				Metadata: metadata,
			});

			await r2Client.send(command);

			return {
				key,
				url: `${R2_PUBLIC_URL}/${key}`,
				size: buffer.length,
				contentType,
			};
		} catch (error) {
			console.error('Error uploading file to R2:', error);
			throw new Error('Failed to upload file');
		}
	}

	/**
	 * Delete a file from R2 storage
	 */
	async deleteFile(key: string): Promise<void> {
		try {
			const command = new DeleteObjectCommand({
				Bucket: R2_BUCKET_NAME,
				Key: key,
			});

			await r2Client.send(command);
		} catch (error) {
			console.error('Error deleting file from R2:', error);
			throw new Error('Failed to delete file');
		}
	}

	/**
	 * Generate a presigned URL for direct upload (optional, for large files)
	 */
	async getPresignedUploadUrl(key: string, contentType: string, expiresIn = 3600): Promise<string> {
		try {
			const command = new PutObjectCommand({
				Bucket: R2_BUCKET_NAME,
				Key: key,
				ContentType: contentType,
			});

			return await getSignedUrl(r2Client, command, { expiresIn });
		} catch (error) {
			console.error('Error generating presigned URL:', error);
			throw new Error('Failed to generate upload URL');
		}
	}

	/**
	 * Generate a unique file key
	 */
	generateFileKey(userId: string, type: 'avatar' | 'attachment', originalName: string): string {
		const timestamp = Date.now();
		const randomString = Math.random().toString(36).substring(2, 15);
		const extension = originalName.split('.').pop();

		return `${type}s/${userId}/${timestamp}-${randomString}.${extension}`;
	}

	/**
	 * Validate file type and size
	 */
	validateFile(file: File, type: 'avatar' | 'attachment'): { valid: boolean; error?: string } {
		const maxSize = 5 * 1024 * 1024; // 5MB

		if (file.size > maxSize) {
			return { valid: false, error: 'File size must be less than 5MB' };
		}

		if (type === 'avatar') {
			const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
			if (!allowedTypes.includes(file.type)) {
				return { valid: false, error: 'Avatar must be an image (JPEG, PNG, GIF, or WebP)' };
			}
		} else if (type === 'attachment') {
			const allowedTypes = [
				// Images
				'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
				// Documents
				'application/pdf', 'text/plain', 'application/msword',
				'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
				// Archives
				'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed',
				// Other
				'application/json', 'text/csv'
			];

			if (!allowedTypes.includes(file.type)) {
				return { valid: false, error: 'File type not supported' };
			}
		}

		return { valid: true };
	}

	/**
	 * Get file URL from key
	 */
	getFileUrl(key: string): string {
		return `${R2_PUBLIC_URL}/${key}`;
	}
}

export default R2StorageService.getInstance();
