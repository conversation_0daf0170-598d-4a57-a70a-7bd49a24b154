import { json } from '@sveltejs/kit';
import jwt from 'jsonwebtoken';
import type { RequestHandler } from '@sveltejs/kit';
import User from '$lib/models/User';

export const GET: RequestHandler = async ({ cookies }) => {
	// Change to use cookies instead of headers
	const token = cookies.get('token'); // Get token from cookies

	if (!token) {
		return json({ error: 'No token provided' }, { status: 401 });
	}

	try {
		const jwtSecret = 'your-secret-key'; // Use same secret as login endpoint
		const decoded = jwt.verify(token, jwtSecret) as unknown;

		if (typeof decoded === 'object' && decoded !== null && 'userId' in decoded) {
			const userId = (decoded as { userId: string }).userId;
			const user = await User.findById(userId);

			if (!user) {
				return json({ error: 'Invalid token' }, { status: 401 });
			}

			return json({ username: user.username });
		}

		return json({ error: 'Invalid token' }, { status: 401 });
	} catch (err) {
		console.error('Error verifying token:', err);
		return json({ error: 'Invalid token' }, { status: 401 });
	}
};
