<script lang="ts">
	import { onMount, onDestroy, tick } from 'svelte'; // Import tick
	import { auth } from '$lib/stores/auth';
	import { goto } from '$app/navigation';
	// Correct the import path for SignalingService - it should likely include formatTimestamp if it's defined there
	// or import formatTimestamp separately if it's in a utils file.
	import { SignalingService } from '$lib/services/SignalingService';
	import { formatDistanceToNow } from 'date-fns'; // Import date-fns directly

	import CallInterface from '$lib/components/CallInterface.svelte';
	import IncomingCallNotification from '$lib/components/IncomingCallNotification.svelte';

	// --- State Variables ---
	let currentUsername: string = '';
	let currentUserId: string | null = null; // Store user ID if possible for creator checks
	let users: string[] = [];
	let groups: {
		_id: string;
		name: string;
		members: { username: string }[];
		createdBy: { username: string };
	}[] = [];
	let selectedUser: string | null = null;
	let selectedGroup: string | null = null;
	let messages: { _id: string; sender: string; message: string; timestamp: Date }[] = []; // Add _id
	let newMessage = '';
	let messagePollingInterval: NodeJS.Timeout | undefined;
	let isCreatingGroup = false;
	let newGroupName = '';
	let selectedMembers: string[] = [];
	let isDirectMessages = true;
	let signalingService: SignalingService | null = null;

	// Loading states
	let loadingUsers = true;
	let loadingGroups = true;
	let loadingMessages = false;
	let initialLoadComplete = false; // Track if initial user/group load is done

	// UI State
	let showCallInterface = false;
	let incomingCallInfo: { from: string; offer: RTCSessionDescription } | null = null;
	let callTargetForInterface: string | null = null;
	let isInterfaceForIncoming: boolean = false;
	let errorMessage = ''; // For displaying errors to the user
	let successMessage = ''; // For displaying success messages

	// Typing indicators state
	let typingUsers = new Map<string, { timeout: NodeJS.Timeout }>(); // Maps username to timeout
	let currentTypingIndicator: string = ''; // Display text

	// --- THIS IS THE CORRECT DECLARATION ---
	let currentUserProfile = {
		username: '',
		profilePicture: null as string | null
	};

	// --- Authentication & Initialization ---
	auth.subscribe((authState) => {
		currentUsername = authState.username || '';
		currentUserProfile.username = currentUsername; // Use the declared variable
		if (currentUsername && !signalingService) {
			ensureSignalingService();
		}
	});

	// --- DELETE THIS DUPLICATE BLOCK ---
	/*
	let currentUserProfile = { // REMOVE THIS
		username: '',          // REMOVE THIS
		profilePicture: null as string | null // REMOVE THIS
	};                           // REMOVE THIS
	 */
	// --- END OF BLOCK TO DELETE ---


	async function ensureSignalingService() {
		if (!signalingService && currentUsername) {
			console.log('Initializing Signaling Service for', currentUsername);
			// Assuming SignalingService constructor is correct
			// If SignalingService has a static 'getOrCreate' method, use that instead.
			signalingService = new SignalingService(currentUsername);
			setupSignalingHandlers();
		}
	}

	// Helper function for timestamp formatting (if not imported)
	function formatTimestamp(timestamp: Date | string): string {
		try {
			const date = new Date(timestamp);
			const now = new Date();
			const diffInSeconds = (now.getTime() - date.getTime()) / 1000;

			if (diffInSeconds < 5) return 'just now';
			if (diffInSeconds < 60) return `${Math.floor(diffInSeconds)}s ago`;
			// Use formatDistanceToNow for times within the last day or so
			if (diffInSeconds < 60 * 60 * 24) {
				// Less than a day
				return formatDistanceToNow(date, { addSuffix: true });
			} else {
				// Format older dates more specifically
				return (
					date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' }) +
					' ' +
					date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' })
				);
			}
		} catch (e) {
			console.error('Error formatting timestamp:', e);
			return new Date(timestamp).toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' }); // Fallback
		}
	}


	function setupSignalingHandlers() {
		if (!signalingService) return; // Guard clause

		signalingService.setCallHandler((from, offer) => {
			if (!showCallInterface) { // Check if interface is already handling a call
				console.log(`Received incoming call notification from ${from}`);
				incomingCallInfo = { from, offer };
				try {
					if (typeof window !== 'undefined') {
						const audio = new Audio('/sounds/ringtone.mp3');
						audio.volume = 0.3;
						audio.play().catch((err) => console.error('Could not play ringtone:', err));
					}
				} catch (e) {
					console.error('Failed to create Audio object for ringtone:', e);
				}
			} else {
				console.log(`Ignoring incoming call from ${from}, call interface already open.`);
				// Optionally send a busy signal back
				// signalingService.sendBusySignal(from); // Implement if needed
			}
		});


		signalingService.setCallRejectedHandler(() => {
			console.log('Received call rejection via global handler');
			if (showCallInterface && callTargetForInterface) {
				// Let CallInterface component handle displaying the rejection status
			} else {
				// Maybe show a brief notification?
				setSuccessMessage('Call rejected by remote user.'); // Or use handleApiError
			}
			// Close the interface if it was open for this rejected outgoing call
			if (showCallInterface && !isInterfaceForIncoming && callTargetForInterface) {
				handleCallEnd(); // Close interface after a short delay maybe?
			}

		});

		signalingService.setTypingHandlers(
			(user, isGroup, targetId) => {
				// onUserTyping
				if ((isGroup && targetId === selectedGroup) || (!isGroup && targetId === selectedUser)) {
					if (user !== currentUsername) { // Don't show self typing
						const timeout = setTimeout(() => clearTyping(user), 3500); // Auto-clear after a bit
						// Clear existing timeout for this user if they keep typing
						const existing = typingUsers.get(user);
						if(existing) clearTimeout(existing.timeout);

						typingUsers.set(user, { timeout });
						updateTypingIndicator();
					}
				}
			},
			(user, isGroup, targetId) => {
				// onUserStoppedTyping
				if ((isGroup && targetId === selectedGroup) || (!isGroup && targetId === selectedUser)) {
					clearTyping(user);
				}
			}
		);

		signalingService.setMessageDeletedHandler((messageId, targetId, isGroup) => {
			console.log(`WS: Message ${messageId} deleted in ${isGroup ? 'group' : 'direct'} ${targetId}`);
			// Check if this deletion pertains to the currently viewed chat
			if (
				(isGroup && targetId === selectedGroup) ||
				(!isGroup && selectedUser === targetId) // Direct message TO selectedUser
				// Note: The original check `targetId === currentUsername && selectedUser === targetId` seems redundant.
				// If selectedUser is targetId, that's the direct chat we care about.
			) {
				messages = messages.filter((msg) => msg._id !== messageId);
			}
		});

		signalingService.setGroupDeletedHandler((groupId) => {
			console.log(`WS: Group ${groupId} deleted`);
			groups = groups.filter((g) => g._id !== groupId);
			if (selectedGroup === groupId) {
				// If the currently selected group was deleted, clear the view
				selectedGroup = null;
				messages = [];
				stopMessagePolling();
				currentTypingIndicator = '';
				typingUsers.clear();
				setSuccessMessage('The current group was deleted.');
				isDirectMessages = true; // Switch to direct messages tab
			}
		});
	}

	// --- Validate Session ---
	async function validateSession() {
		try {
			const res = await fetch('/api/auth/validate', { credentials: 'include' });
			if (!res.ok) {
				handleLogout(); // Use dedicated logout function
			} else {
				// const data = await res.json(); // Optionally get more user data
				// auth.setAuth(...) // Update auth store if needed
				await Promise.all([fetchUsers(), fetchGroups()]); // Fetch initial data
				initialLoadComplete = true;
			}
		} catch (err) {
			console.error('Error validating session:', err);
			handleLogout(); // Use dedicated logout function
		}
	}

	// --- Logout Handling ---
	function handleLogout() {
		auth.clearAuth();
		if (signalingService) {
			signalingService.close(); // Ensure WebSocket is closed
			signalingService = null;
		}
		stopMessagePolling();
		goto('/login');
	}


	// --- Data Fetching ---
	async function fetchUsers() {
		loadingUsers = true;
		try {
			const res = await fetch('/api/users', { credentials: 'include' });
			if (res.ok) {
				const data = await res.json();
				users = (data.users || []).filter((user: string) => user !== currentUsername);
			} else {
				handleApiError('Failed to fetch users', await res.text());
				users = [];
			}
		} catch (err: any) {
			handleApiError('Error fetching users', err.message);
			users = [];
		} finally {
			loadingUsers = false;
		}
	}

	async function fetchMessages() {
		if (!selectedUser) return;
		loadingMessages = true;
		errorMessage = ''; // Clear previous errors
		try {
			const res = await fetch(`/api/chat/messages?receiver=${selectedUser}`, {
				credentials: 'include'
			});
			if (res.ok) {
				const fetchedMessages = await res.json();
				// Ensure timestamp is a Date object and messages have _id
				messages = (fetchedMessages || []).map((m: any) => ({
					...m,
					_id: m._id || `client-${Math.random()}`, // Assign client-side ID if missing (shouldn't happen ideally)
					timestamp: new Date(m.timestamp)
				}));
				await tick(); // Wait for DOM update before scrolling
				scrollToBottom();
			} else {
				handleApiError('Failed to fetch messages', await res.text());
				messages = [];
			}
		} catch (err: any) {
			handleApiError('Error fetching messages', err.message);
			messages = [];
		} finally {
			loadingMessages = false;
		}
	}

	async function fetchGroupMessages() {
		if (!selectedGroup) return;
		loadingMessages = true;
		errorMessage = '';
		try {
			const res = await fetch(`/api/chat/messages?group=${selectedGroup}`, {
				credentials: 'include'
			});
			if (res.ok) {
				const fetchedMessages = await res.json();
				messages = (fetchedMessages || []).map((m: any) => ({
					...m,
					_id: m._id || `client-${Math.random()}`,
					timestamp: new Date(m.timestamp)
				}));
				await tick();
				scrollToBottom();
			} else {
				handleApiError('Failed to fetch group messages', await res.text());
				messages = [];
			}
		} catch (err: any) {
			handleApiError('Error fetching group messages', err.message);
			messages = [];
		} finally {
			loadingMessages = false;
		}
	}

	async function fetchGroups() {
		loadingGroups = true;
		try {
			const res = await fetch('/api/groups', { credentials: 'include' });
			if (res.ok) {
				const data = await res.json();
				groups = data.groups || []; // Ensure it's an array
			} else {
				handleApiError('Failed to fetch groups', await res.text());
				groups = [];
			}
		} catch (err: any) {
			handleApiError('Error fetching groups', err.message);
			groups = [];
		} finally {
			loadingGroups = false;
		}
	}

	// --- Actions (SendMessage, CreateGroup, DeleteMessage, DeleteGroup) ---
	async function sendMessage() {
		if (!newMessage.trim() || (!selectedUser && !selectedGroup) || !signalingService) return;

		const messageToSend = newMessage.trim();
		const tempId = `temp-${Date.now()}`;

		// Optimistic UI update
		const optimisticMessage = {
			_id: tempId,
			sender: currentUsername,
			message: messageToSend,
			timestamp: new Date()
		};
		messages = [...messages, optimisticMessage];
		newMessage = ''; // Clear input *after* storing value
		await tick();
		scrollToBottom();

		// Stop sending typing indicator AFTER sending the message
		const currentMembers = selectedGroup
			? groups.find((g) => g._id === selectedGroup)?.members.map((m) => m.username) ?? []
			: [];
		signalingService.sendStoppedTyping(
			selectedUser || '',
			!!selectedGroup,
			selectedGroup || selectedUser || '',
			currentMembers
		);

		const body = selectedUser
			? { receiver: selectedUser, message: messageToSend }
			: { groupId: selectedGroup, message: messageToSend };

		try {
			const res = await fetch('/api/chat/send', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				credentials: 'include',
				body: JSON.stringify(body)
			});

			if (!res.ok) {
				handleApiError('Error sending message', await res.text());
				// Remove optimistic message on failure
				messages = messages.filter((m) => m._id !== tempId);
			} else {
				// Message sent successfully, refetch to get the real message ID/timestamp
				// The polling or a dedicated WS message could also handle this update.
				if (selectedUser) await fetchMessages();
				else await fetchGroupMessages();
				// Ensure the optimistic message is removed or replaced by the fetched one
				messages = messages.filter(m => m._id !== tempId);
			}
		} catch (err: any) {
			handleApiError('Error sending message', err.message);
			// Remove optimistic message on network failure
			messages = messages.filter((m) => m._id !== tempId);
		}
	}


	async function createGroup() {
		if (!newGroupName.trim() || selectedMembers.length === 0) {
            handleApiError("Create Group Error", "Group name cannot be empty and must have at least one member.");
            return;
        };

		try {
			const res = await fetch('/api/groups', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				credentials: 'include',
				body: JSON.stringify({ name: newGroupName.trim(), members: selectedMembers })
			});

			if (res.ok) {
				const data = await res.json();
				isCreatingGroup = false;
				newGroupName = '';
				selectedMembers = [];
				await fetchGroups(); // Refresh group list
				setSuccessMessage('Group created successfully!');
				if (data.group?._id) { // Check if group ID exists
					selectGroup(data.group._id); // Select the new group
				}
            } else {
				handleApiError('Error creating group', await res.text());
			}
		} catch (err: any) {
			handleApiError('Error creating group', err.message);
		}
	}


    async function deleteMessage(messageId: string) {
		if (!signalingService || !confirm('Are you sure you want to delete this message? This cannot be undone.')) return;

		const originalMessages = [...messages];
		messages = messages.filter(msg => msg._id !== messageId); // Optimistic UI update

		try {
			const res = await fetch(`/api/chat/messages/${messageId}`, {
				method: 'DELETE',
				credentials: 'include'
			});
			const data = await res.json();

			if (res.ok && data.success) {
				setSuccessMessage('Message deleted.');
				// Notify other clients via WebSocket
                 const currentMembers = selectedGroup ? groups.find(g => g._id === selectedGroup)?.members.map(m => m.username) ?? [] : [];
                signalingService.broadcastMessageDeleted(data.deletedMessageId, data.conversationTarget, data.isGroup, currentMembers);
			} else {
				handleApiError('Failed to delete message', data.error || 'Server error');
				messages = originalMessages; // Revert optimistic update
			}
		} catch (err: any) {
			handleApiError('Error deleting message', err.message);
			messages = originalMessages; // Revert optimistic update
		}
	}

	async function deleteGroup() {
		const groupToDelete = groups.find((g) => g._id === selectedGroup);
		if (!selectedGroup || !groupToDelete || !signalingService || !confirm(`Are you sure you want to delete the group "${groupToDelete.name}"? This will delete all messages and cannot be undone.`)) return;


		const memberUsernames = groupToDelete.members.map((m) => m.username);
		const deletedGroupId = selectedGroup; // Store before clearing state

		// Optimistic UI update
		const originalGroups = [...groups];
		groups = groups.filter((g) => g._id !== deletedGroupId);
		selectedGroup = null; // Clear selection
		messages = [];
		stopMessagePolling();
		currentTypingIndicator = '';
		typingUsers.clear();
		isDirectMessages = true; // Switch tab

		try {
			const res = await fetch(`/api/groups/${deletedGroupId}`, {
				method: 'DELETE',
				credentials: 'include'
			});
			const data = await res.json();

			if (res.ok && data.success) {
				setSuccessMessage('Group deleted.');
				// Notify members via WebSocket
				signalingService.broadcastGroupDeleted(data.deletedGroupId, data.memberUsernames);
			} else {
				handleApiError('Failed to delete group', data.error || 'Server error');
				groups = originalGroups; // Revert optimistic update
				// Maybe try to re-select the group if revert is needed? Requires more state.
                isDirectMessages = false; // Switch back tab? Complex state restoration needed.
                await fetchGroups(); // Or just refetch
			}
		} catch (err: any) {
			handleApiError('Error deleting group', err.message);
			groups = originalGroups; // Revert optimistic update
            await fetchGroups(); // Refetch
		}
	}


	// --- UI Logic & Event Handlers ---
	function selectUser(username: string) {
		if (username === currentUsername || selectedUser === username) return;
		stopMessagePolling(); // Stop polling for previous chat first
		isDirectMessages = true;
		selectedUser = username;
		selectedGroup = null;
		messages = [];
		currentTypingIndicator = '';
		typingUsers.clear();
		startMessagePolling(); // Start polling for new selection
	}

	function selectGroup(groupId: string) {
		if (selectedGroup === groupId) return;
		stopMessagePolling(); // Stop polling for previous chat first
		isDirectMessages = false;
		selectedGroup = groupId;
		selectedUser = null;
		messages = [];
		currentTypingIndicator = '';
		typingUsers.clear();
		startGroupMessagePolling(); // Start polling for new selection
	}

	function startMessagePolling() {
		if (!selectedUser) return;
		stopMessagePolling(); // Ensure only one interval runs
		fetchMessages(); // Initial fetch
		messagePollingInterval = setInterval(fetchMessages, 5000); // Poll every 5 seconds
	}

	function startGroupMessagePolling() {
		if (!selectedGroup) return;
		stopMessagePolling(); // Ensure only one interval runs
		fetchGroupMessages(); // Initial fetch
		messagePollingInterval = setInterval(fetchGroupMessages, 5000); // Poll every 5 seconds
	}

	function stopMessagePolling() {
		if (messagePollingInterval) {
			clearInterval(messagePollingInterval);
			messagePollingInterval = undefined;
		}
	}

	function handleInput() {
		if (!signalingService || (!selectedUser && !selectedGroup)) return;

		const target = selectedUser || selectedGroup || '';
		const isGroup = !!selectedGroup;
		const members = isGroup ? groups.find((g) => g._id === target)?.members.map((m) => m.username) ?? [] : [];

		signalingService.sendTyping(target, isGroup, target, members);
	}

	// Typing indicator logic
	function updateTypingIndicator() {
		const usersTyping = Array.from(typingUsers.keys());
		if (usersTyping.length === 0) {
			currentTypingIndicator = '';
		} else if (usersTyping.length === 1) {
			currentTypingIndicator = `${usersTyping[0]} is typing...`;
		} else if (usersTyping.length === 2) {
			currentTypingIndicator = `${usersTyping[0]} and ${usersTyping[1]} are typing...`;
		} else {
			currentTypingIndicator = `Several people are typing...`;
		}
	}

	function clearTyping(user: string) {
		const userTyping = typingUsers.get(user);
		if (userTyping) {
			clearTimeout(userTyping.timeout);
			typingUsers.delete(user);
			updateTypingIndicator();
		}
	}

	// Error and Success message handling
	function handleApiError(context: string, errorDetails: string) {
		console.error(`${context}:`, errorDetails);
		errorMessage = `${context}: ${String(errorDetails).substring(0, 100)}${
			String(errorDetails).length > 100 ? '...' : ''
		}`;
		successMessage = '';
		setTimeout(() => (errorMessage = ''), 5000); // Clear error after 5s
	}

	function setSuccessMessage(message: string) {
		successMessage = message;
		errorMessage = '';
		setTimeout(() => (successMessage = ''), 3000); // Clear success after 3s
	}

	// --- Call Handling (Keep existing functions) ---
	function startCall() {
		ensureSignalingService().then(() => {
			if (selectedUser && signalingService) { // Check signalingService exists
				console.log(`Initiating call interface for outgoing call to ${selectedUser}`);
				incomingCallInfo = null;
				callTargetForInterface = selectedUser;
				isInterfaceForIncoming = false;
				showCallInterface = true;
			} else if (!selectedUser) {
                handleApiError("Call Error", "Select a user to start a call.");
            }
		});
	}

	function acceptIncomingCall() {
		if (incomingCallInfo && signalingService) { // Check signalingService exists
			console.log(`Accepting call from ${incomingCallInfo.from} - showing interface`);
			callTargetForInterface = incomingCallInfo.from;
			isInterfaceForIncoming = true;
			showCallInterface = true;
            // Don't clear incomingCallInfo, CallInterface needs it
		}
	}

	function rejectIncomingCall() {
		if (incomingCallInfo && signalingService) {
			console.log(`Rejecting call from ${incomingCallInfo.from}`);
			signalingService.sendCallRejection(incomingCallInfo.from);
			incomingCallInfo = null; // Clear notification state
		}
	}

	function handleCallEnd() {
		console.log('Call interface closed. Resetting state.');
		showCallInterface = false;
		incomingCallInfo = null; // Clear any call state
		callTargetForInterface = null;
		isInterfaceForIncoming = false;
	}

	// --- Lifecycle & DOM ---
	onMount(() => {
		validateSession();
		// Check for ringtone (optional)
		// ... fetch('/sounds/ringtone.mp3')...
	});

	onDestroy(() => {
		stopMessagePolling();
		if (signalingService) {
			signalingService.close();
			signalingService = null;
		}
		typingUsers.forEach((t) => clearTimeout(t.timeout)); // Clear any pending timers
	});

	let messageContainer: HTMLDivElement;
	let isScrolledToBottom = true;

	function checkScroll() {
		if (!messageContainer) return;
		const threshold = 20; // Pixels from bottom tolerance
		isScrolledToBottom =
			messageContainer.scrollHeight - messageContainer.scrollTop - messageContainer.clientHeight <
			threshold;
	}

	function scrollToBottom(force = false) {
        // Only auto-scroll if user was already near the bottom, or if forced (e.g., selecting new chat)
		if (messageContainer && (isScrolledToBottom || force)) {
            // Use requestAnimationFrame for smoother scrolling after DOM updates
            requestAnimationFrame(() => {
                if (messageContainer) { // Check again inside RAF
                    messageContainer.scrollTop = messageContainer.scrollHeight;
                }
            });
		}
	}

	// Reactive scroll handling
	$: if (messageContainer && messages.length) {
		// Don't auto-scroll immediately on every message update,
		// let the fetchMessages/sendMessage functions handle it after tick()
		// Check scroll position on incoming messages if needed
	}

	// Reactive block for tab switching logic
	$: {
		if (isDirectMessages) {
			if (selectedGroup) {
				// Switched FROM Groups TO Direct
				stopMessagePolling();
				selectedGroup = null;
				messages = [];
                currentTypingIndicator = '';
                typingUsers.clear();
				// If a user was selected previously, maybe re-select? Usually better to clear.
				// if (previousSelectedUser) selectUser(previousSelectedUser);
			}
		} else {
			// Switched FROM Direct TO Groups
			if (selectedUser) {
				stopMessagePolling();
				selectedUser = null;
				messages = [];
                currentTypingIndicator = '';
                typingUsers.clear();
                // if (previousSelectedGroup) selectGroup(previousSelectedGroup);
			}
		}
	}

	// Reactive computed values
	$: isGroupCreator =
		selectedGroup && groups.find((g) => g._id === selectedGroup)?.createdBy?.username === currentUsername;
	$: currentChatName = selectedUser
		? selectedUser
		: selectedGroup
			? groups.find((g) => g._id === selectedGroup)?.name || 'Group'
			: 'Select Chat';
	$: currentChatAvatarLetter = selectedUser
		? selectedUser[0]?.toUpperCase() || '?'
		: selectedGroup
			? '#'
			: '?';

    $: selectedGroupName = selectedGroup ? groups.find((g) => g._id === selectedGroup)?.name : null;

</script>

<!-- Outer container -->
<div class="flex h-screen bg-slate-50 text-sm antialiased">
	<!-- Sidebar -->
	<div class="flex w-64 flex-shrink-0 flex-col border-r border-gray-200 bg-white">
		<!-- Header -->
		<div class="flex h-16 flex-shrink-0 items-center justify-between border-b border-gray-200 px-4">
			<a href="/" class="font-mono text-xl font-semibold text-black">konekt</a>
			<button
				on:click={handleLogout}
				class="rounded p-1 text-gray-500 hover:bg-gray-100 hover:text-red-600"
				title="Logout"
				aria-label="Logout"
			>
				<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
			</button>
		</div>

		<!-- Tabs -->
		<div class="flex border-b border-gray-200">
			<button
				class={`flex-1 py-2 px-3 font-mono text-xs font-medium uppercase tracking-wider transition-colors duration-150 ${isDirectMessages ? 'border-b-2 border-black text-black' : 'text-gray-500 hover:bg-gray-50 hover:text-black'}`}
				on:click={() => (isDirectMessages = true)}
			>
				Direct
			</button>
			<button
				class={`flex-1 py-2 px-3 font-mono text-xs font-medium uppercase tracking-wider transition-colors duration-150 ${!isDirectMessages ? 'border-b-2 border-black text-black' : 'text-gray-500 hover:bg-gray-50 hover:text-black'}`}
				on:click={() => (isDirectMessages = false)}
			>
				Groups
			</button>
		</div>

		<!-- Users/Groups List -->
		<div class="flex-1 overflow-y-auto p-2">
			{#if isDirectMessages}
				{#if loadingUsers}
					<div class="p-4 text-center text-xs text-gray-400">Loading users...</div>
				{:else if users.length > 0}
					{#each users as user (user)}
						<button
							class={`mb-1 flex w-full items-center gap-2 rounded p-2 text-left font-mono text-sm transition-colors duration-150 ${
								selectedUser === user
									? 'bg-black text-white'
									: 'text-gray-700 hover:bg-gray-100'
							}`}
							on:click={() => selectUser(user)}
						>
							<div class="flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-gray-300 text-xs font-semibold uppercase text-gray-600">
								{user[0] || '?'}
							</div>
							<span class="truncate">{user}</span>
						</button>
					{/each}
				{:else if initialLoadComplete}
					<p class="p-4 text-center text-xs text-gray-400">No other users found.</p>
				{/if}
			{:else}
				{#if loadingGroups}
					<div class="p-4 text-center text-xs text-gray-400">Loading groups...</div>
				{:else if groups.length > 0}
					{#each groups as group (group._id)}
						<button
							class={`mb-1 flex w-full items-center gap-2 rounded p-2 text-left font-mono text-sm transition-colors duration-150 ${
								selectedGroup === group._id
									? 'bg-black text-white'
									: 'text-gray-700 hover:bg-gray-100'
							}`}
							on:click={() => selectGroup(group._id)}
						>
							<span class="text-gray-400">#</span>
							<span class="truncate">{group.name}</span>
						</button>
					{/each}
				{:else if initialLoadComplete}
					<p class="p-4 text-center text-xs text-gray-400">No groups yet. Create one!</p>
				{/if}
				<button
					class="mt-3 flex w-full items-center justify-center gap-1 rounded border border-gray-300 p-2 font-mono text-xs text-gray-600 transition-colors hover:border-black hover:text-black disabled:cursor-not-allowed disabled:opacity-50"
					on:click={() => (isCreatingGroup = true)}
					disabled={users.length === 0 || loadingUsers}
					title={users.length === 0 ? 'No other users available to create a group' : 'Create a new group'}
				>
					<svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"> <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /> </svg>
					Create Group
				</button>
			{/if}

			<!-- Create Group Form -->
			{#if isCreatingGroup}
				<div
					class="mt-4 rounded border border-gray-200 bg-gray-50 p-3 shadow-sm"
					transition:fade={{ duration: 200 }}
				>
					  <h3 class="mb-2 text-sm font-semibold">New Group</h3>
					  <div class="space-y-2">
						  <input
							  bind:value={newGroupName}
							  placeholder="Group name"
							  class="w-full rounded border border-gray-300 p-1.5 text-sm focus:border-black focus:ring-1 focus:ring-black"
							  maxlength="50"
						  />
						  <p class="text-xs text-gray-500">Select members:</p>
						  <div class="max-h-32 overflow-y-auto rounded border border-gray-300 bg-white p-1 text-sm">
							  {#each users as user (user)}
								  <label class="flex cursor-pointer items-center rounded px-1.5 py-1 hover:bg-gray-100">
									  <input
										  type="checkbox"
										  bind:group={selectedMembers}
										  value={user}
										  class="mr-2 h-4 w-4 accent-black"
									  />
									  <span>{user}</span>
								  </label>
							  {:else}
									<p class="p-1 text-xs text-gray-400">No users to add.</p>
							  {/each}
						  </div>
						  <div class="flex gap-2 pt-1">
							  <button
								  class="flex-1 rounded bg-black px-2 py-1.5 font-mono text-xs text-white transition-colors hover:bg-gray-800 disabled:opacity-50"
								  on:click={createGroup}
								  disabled={!newGroupName.trim() || selectedMembers.length === 0}
							  >
								  Create
							  </button>
							  <button
								  class="flex-1 rounded border border-gray-300 px-2 py-1.5 font-mono text-xs text-gray-700 hover:bg-gray-100"
								  on:click={() => {
									  isCreatingGroup = false;
									  newGroupName = '';
									  selectedMembers = [];
								  }}
							  >
								  Cancel
							  </button>
						  </div>
					  </div>
				  </div>
			{/if}
		</div>

		<!-- User Profile / Settings Link -->
		<div class="mt-auto flex-shrink-0 border-t border-gray-200 p-3">
			<div class="flex items-center justify-between">
				<div class="flex min-w-0 items-center gap-2">
					<div class="relative h-7 w-7 flex-shrink-0">
						<div class="flex h-full w-full items-center justify-center rounded-full bg-black text-xs font-semibold uppercase text-white">
							{currentUsername && currentUsername.length > 0 ? currentUsername[0] : '?'}
						</div>
						{#if currentUserProfile.profilePicture}
							<img src={currentUserProfile.profilePicture} alt={currentUsername} class="absolute inset-0 h-full w-full rounded-full object-cover" />
						{/if}
						<!-- Online Indicator Example (Optional) -->
						<!-- <div class="absolute bottom-0 right-0 h-2 w-2 rounded-full bg-green-500 border border-white"></div> -->
					</div>

					<span class="truncate font-mono text-sm font-medium" title={currentUsername}>
						{currentUsername}
					</span>
				</div>
				<button
					on:click={() => goto('/settings')}
					class="flex-shrink-0 rounded p-1 text-gray-500 transition-colors hover:bg-gray-100 hover:text-black"
					title="Settings"
					aria-label="Settings"
				>
					<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
						<path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
					</svg>
				</button>
			</div>
			<!-- Global Error/Success Messages -->
			{#if errorMessage}
				<div class="mt-2 max-w-full truncate rounded border border-red-200 bg-red-50 p-1 px-2 text-xs text-red-700" title={errorMessage}>
					{errorMessage}
				</div>
			{/if}
			{#if successMessage}
				<div class="mt-2 max-w-full truncate rounded border border-green-200 bg-green-50 p-1 px-2 text-xs text-green-700">
					{successMessage}
				</div>
			{/if}
		</div>
	</div>

	<!-- Chat Area -->
	<div class="flex flex-1 flex-col bg-gray-50">
		{#if selectedUser || selectedGroup}
			<!-- Chat Header -->
			<div class="flex h-16 flex-shrink-0 items-center justify-between border-b border-gray-200 bg-white px-5">
				<div class="flex min-w-0 items-center gap-3">
					<div class="relative h-8 w-8 flex-shrink-0">
						 <div class="flex h-full w-full items-center justify-center rounded-full bg-black text-sm font-semibold uppercase text-white">
                           {currentChatAvatarLetter}
						</div>
						<!-- Online indicator can go here if needed -->
					</div>
					<div>
						<h2 class="truncate font-mono text-base font-semibold leading-tight">
							 {currentChatName}
						</h2>
						<div class="h-4"> <!-- Container for typing indicator to prevent layout shifts -->
							{#if currentTypingIndicator}
								<p class="truncate text-xs text-gray-500">{currentTypingIndicator}</p>
							{/if}
						</div>
					</div>
				</div>
                <div class="flex items-center gap-2">
                    {#if selectedUser}
                        <button
                            on:click={startCall}
                            class="ml-auto flex-shrink-0 rounded-full p-1.5 text-gray-500 transition-colors hover:bg-gray-100 hover:text-green-600"
                            title={`Start voice call with ${selectedUser}`}
                            aria-label={`Start voice call with ${selectedUser}`}
                        >
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                             </svg>
                        </button>
                    {/if}
                    {#if selectedGroup && isGroupCreator}
                         <button
                            on:click={deleteGroup}
                            class="ml-auto flex-shrink-0 rounded-full p-1.5 text-gray-500 transition-colors hover:bg-red-50 hover:text-red-600"
                            title={`Delete group "${selectedGroupName}"`}
                            aria-label={`Delete group "${selectedGroupName}"`}
                         >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                              <path stroke-linecap="round" stroke-linejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    {/if}
                </div>
			</div>

			<!-- Message List -->
			<div bind:this={messageContainer} class="flex-1 space-y-1.5 overflow-y-auto p-4" on:scroll={checkScroll} >
				{#if loadingMessages && messages.length === 0}
					<div class="py-5 text-center text-xs text-gray-400">Loading messages...</div>
				{/if}
				{#each messages as msg (msg._id)}
					<div class={`group flex items-end ${msg.sender === currentUsername ? 'justify-end' : 'justify-start'}`}>
						{#if msg.sender === currentUsername}
							<button
								class="mb-1 mr-1 rounded p-0.5 text-gray-400 opacity-0 transition-opacity group-hover:opacity-100 hover:bg-red-100 hover:text-red-600"
								title="Delete message" aria-label="Delete message"
								on:click={() => deleteMessage(msg._id)}
							>
								 <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
							</button>
						{/if}
						<div
							class={`relative max-w-[70%] rounded-lg px-3 py-1.5 shadow-sm ${
								msg.sender === currentUsername ? 'bg-black text-white' : 'border border-gray-100 bg-white text-black'
							}`}
						>
							{#if selectedGroup && msg.sender !== currentUsername}
								<span class="mb-0.5 block text-xs font-semibold text-blue-600 opacity-90">{msg.sender}</span>
							{/if}
							<p class="break-words text-sm leading-snug">{msg.message}</p>
							<p class={`mt-1 text-right text-[10px] opacity-60 ${msg.sender === currentUsername ? 'text-gray-300' : 'text-gray-500'}`}>
								{formatTimestamp(msg.timestamp)}
							</p>
						</div>
					</div>
				{:else if !loadingMessages && messages.length === 0}
					<p class="pt-4 text-center text-xs text-gray-400">
						{#if selectedUser}Start a conversation with {selectedUser}.{/if}
						{#if selectedGroup}Start the conversation in #{currentChatName}.{/if}
					</p>
				{/each}
			</div>

			<!-- Message Input -->
			<div class="flex-shrink-0 border-t border-gray-200 bg-white p-3">
				<div class="flex items-center gap-2">
					<input
						bind:value={newMessage}
						on:input={handleInput}
						placeholder="Type a message..."
						class="flex-1 rounded-lg border border-gray-300 bg-gray-100 px-3 py-2 font-sans text-sm focus:border-black focus:bg-white focus:outline-none focus:ring-1 focus:ring-black"
						on:keydown={(e) => {
							if (e.key === 'Enter' && !e.shiftKey) {
								e.preventDefault();
								sendMessage();
							}
						}}
					/>
					<button
						on:click={sendMessage}
						class="flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-lg bg-black text-white transition-colors duration-150 hover:bg-gray-800 disabled:opacity-50"
						disabled={!newMessage.trim()}
						title="Send message" aria-label="Send message"
					>
						<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
							<path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 16.571V11a1 1 0 112 0v5.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
						</svg>
					</button>
				</div>
			</div>
		{:else}
			<!-- Placeholder when no chat is selected -->
			 <div class="flex flex-1 flex-col items-center justify-center bg-gray-100 p-8 text-center">
                 <svg xmlns="http://www.w3.org/2000/svg" class="mb-4 h-16 w-16 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
				<p class="font-mono text-base text-gray-500">Select a user or group</p>
                <p class="mt-1 text-sm text-gray-400">to start chatting or make a call.</p>
			</div>
		{/if}
	</div>

	<!-- Call Interface & Notification Modals -->
	{#if showCallInterface}
		<CallInterface
			username={currentUsername}
			remotePeer={isInterfaceForIncoming ? null : callTargetForInterface}
			isIncoming={isInterfaceForIncoming}
			incomingCallData={isInterfaceForIncoming ? incomingCallInfo : null}
			on:close={handleCallEnd}
		/>
	{/if}

	{#if !showCallInterface && incomingCallInfo}
		<IncomingCallNotification
			from={incomingCallInfo.from}
			on:accept={acceptIncomingCall}
			on:reject={rejectIncomingCall}
		/>
	{/if}
</div>
