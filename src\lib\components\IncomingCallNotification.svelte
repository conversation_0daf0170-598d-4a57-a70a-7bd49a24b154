<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	export let from: string;

	const dispatch = createEventDispatcher();

	function acceptCall() {
		dispatch('accept');
	}

	function rejectCall() {
		dispatch('reject');
	}
</script>

<div class="fixed bottom-8 right-8 z-40 flex w-80 flex-col gap-4 rounded-lg bg-white p-4 shadow-lg">
	<div class="flex items-center justify-between">
		<div class="flex items-center space-x-3">
			<div class="h-10 w-10 animate-pulse rounded-full bg-black">
				<div class="flex h-full w-full items-center justify-center text-xl text-white">
					{from[0].toUpperCase()}
				</div>
			</div>
			<div>
				<p class="font-semibold">{from}</p>
				<p class="text-sm text-gray-500">Incoming call...</p>
			</div>
		</div>
	</div>

	<div class="flex justify-end space-x-2">
		<button
			on:click={acceptCall}
			class="rounded-full bg-green-500 p-3 text-white hover:bg-green-600"
		>
			<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
				/>
			</svg>
		</button>
		<button on:click={rejectCall} class="rounded-full bg-red-500 p-3 text-white hover:bg-red-600">
			<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M6 18L18 6M6 6l12 12"
				/>
			</svg>
		</button>
	</div>
</div>
