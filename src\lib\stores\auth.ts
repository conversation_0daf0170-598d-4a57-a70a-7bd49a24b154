import { writable } from 'svelte/store';

interface AuthState {
	token: string | null;
	username: string | null;
}

function createAuthStore() {
	const { subscribe, set } = writable<AuthState>({
		token: null,
		username: null
	});

	return {
		subscribe,
		setAuth: (token: string, username: string) => {
			set({ token, username });
		},
		clearAuth: () => {
			set({ token: null, username: null });
		}
	};
}

export const auth = createAuthStore();
