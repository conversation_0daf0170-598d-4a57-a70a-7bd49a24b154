import { WebSocketServer } from 'ws';
import { parse } from 'url';

const wss = new WebSocketServer({ port: 3000 });

const clients = new Map<string, WebSocket>();

wss.on('connection', (ws, req) => {
	const { query } = parse(req.url!, true);
	const username = query.username as string;

	if (username) {
		clients.set(username, ws);
	}

	ws.on('message', (data) => {
		const message = JSON.parse(data.toString());
		const targetClient = clients.get(message.to);

		if (targetClient) {
			targetClient.send(
				JSON.stringify({
					...message,
					from: username
				})
			);
		}
	});

	ws.on('close', () => {
		clients.delete(username);
	});
});
