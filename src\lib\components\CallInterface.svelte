<script lang="ts">
	import { onMount, onDestroy, createEventDispatcher } from 'svelte';
	import { SignalingService } from '$lib/services/SignalingService';
	import { CallService } from '$lib/services/CallService';

	const dispatch = createEventDispatcher();

	export let username: string;
	export let remotePeer: string | null = null;
	export let isIncoming: boolean = false;
	export let incomingCallData: { from: string; offer: RTCSessionDescription } | null = null;

	let callService: CallService;
	let signalingService: SignalingService;
	let isInCall = false;
	let currentCallStatus = '';
	let callDuration = 0;
	let callTimer: ReturnType<typeof setInterval> | undefined;
	let initialized = false;
	let ringtoneAudio: HTMLAudioElement | undefined;
	let internalIncomingOffer: RTCSessionDescription | null = null;

	onMount(() => {
		if (initialized) return;

		// Initialize services once
		signalingService = SignalingService.getOrCreate(username);
		callService = new CallService(signalingService);

		// Setup call status handler
		callService.setCallStatusHandler((status) => {
			currentCallStatus = status;

			// Update call states based on status
			if (status === 'Connected') {
				isInCall = true;
				stopRingtone();
				startCallTimer();
			} else if (status === 'Calling...') {
				isInCall = true;
				playRingtone();
			} else if (status === 'Call ended' || status.includes('Failed')) {
				isInCall = false;
				stopRingtone();
				endCallCleanup();
				setTimeout(() => dispatch('close'), 2000);
			}
		});

		// Setup incoming call handler if needed
		if (!isIncoming && !incomingCallData) {
			callService.setIncomingCallHandler((from, offer) => {
				console.warn('Unexpected incoming call - interface already open');
			});
		}

		signalingService.setCallRejectedHandler(() => {
			currentCallStatus = 'Call rejected';
			isInCall = false;
			stopRingtone();
			setTimeout(() => {
				endCallCleanup();
				dispatch('close');
			}, 2000);
		});

		// Initialize audio for ringtone
		if (typeof window !== 'undefined') {
			ringtoneAudio = new Audio('/sounds/ringtone.mp3');
			ringtoneAudio.loop = true;
			ringtoneAudio.volume = 0.3;
		}

		// Set initial state based on props
		if (isIncoming && incomingCallData) {
			currentCallStatus = `Incoming call from ${incomingCallData.from}...`;
			internalIncomingOffer = incomingCallData.offer;
			playRingtone();
		} else if (remotePeer) {
			currentCallStatus = `${remotePeer} - Start call?`;
		} else {
			currentCallStatus = 'No call target specified';
		}

		initialized = true;
	});

	function playRingtone() {
		if (!ringtoneAudio) return;
		try {
			ringtoneAudio.play().catch(console.error);
		} catch (error) {
			console.error('Error playing ringtone:', error);
		}
	}

	function stopRingtone() {
		if (!ringtoneAudio) return;
		try {
			ringtoneAudio.pause();
			ringtoneAudio.currentTime = 0;
		} catch (error) {
			console.error('Error stopping ringtone:', error);
		}
	}

	function startCallTimer() {
		clearCallTimer();
		callDuration = 0;
		callTimer = setInterval(() => {
			callDuration++;
		}, 1000);
	}

	function clearCallTimer() {
		if (callTimer) {
			clearInterval(callTimer);
			callTimer = undefined;
		}
	}

	function formatDuration(seconds: number) {
		const mins = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${mins}:${secs.toString().padStart(2, '0')}`;
	}

	async function initiateCall() {
		if (!remotePeer) return;

		isInCall = true;
		try {
			await callService.startCall(remotePeer);
		} catch (error) {
			console.error('Error starting call:', error);
			currentCallStatus = 'Failed to start call';
		}
	}

	async function acceptCall() {
		if (!internalIncomingOffer || !incomingCallData?.from) return;

		isInCall = true;
		stopRingtone();
		try {
			await callService.handleIncomingCall(incomingCallData.from, internalIncomingOffer);
			internalIncomingOffer = null;
		} catch (error) {
			console.error('Error accepting call:', error);
			currentCallStatus = 'Failed to connect';
		}
	}

	function rejectCall() {
		if (!incomingCallData?.from) return;
		callService.rejectCall();
		stopRingtone();
		endCallCleanup();
		dispatch('close');
	}

	function endCall() {
		callService.endCall();
		stopRingtone();
	}

	function cancelCall() {
		if (isInCall) {
			endCall();
		} else {
			stopRingtone();
			endCallCleanup();
			dispatch('close');
		}
	}

	function endCallCleanup() {
		clearCallTimer();
		isInCall = false;
	}

	onDestroy(() => {
		clearCallTimer();
		stopRingtone();

		if (callService) {
			callService.destroy();
		}
	});
</script>

<div
	class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
>
	<div class="relative w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
		<!-- Close button -->
		<button
			on:click={cancelCall}
			class="absolute right-4 top-4 rounded-full p-2 text-gray-500 hover:bg-gray-100"
			aria-label="Close call interface"
		>
			<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M6 18L18 6M6 6l12 12"
				/>
			</svg>
		</button>

		{#if isIncoming && !isInCall}
			<!-- Incoming call UI -->
			<div class="flex flex-col items-center space-y-6">
				<div class="h-24 w-24 animate-pulse overflow-hidden rounded-full bg-black">
					<div class="flex h-full w-full items-center justify-center text-4xl text-white">
						{incomingCallData?.from?.[0]?.toUpperCase() || '?'}
					</div>
				</div>
				<div class="text-center">
					<h3 class="text-xl font-semibold">{incomingCallData?.from || 'Unknown caller'}</h3>
					<p class="mt-1 text-sm text-gray-500">{currentCallStatus}</p>
				</div>
				<div class="flex items-center space-x-6">
					<button
						on:click={acceptCall}
						class="flex h-16 w-16 items-center justify-center rounded-full bg-green-500 text-white transition-colors hover:bg-green-600"
						aria-label="Accept call"
					>
						<svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
							/>
						</svg>
					</button>
					<button
						on:click={rejectCall}
						class="flex h-16 w-16 items-center justify-center rounded-full bg-red-500 text-white transition-colors hover:bg-red-600"
						aria-label="Reject call"
					>
						<svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M6 18L18 6M6 6l12 12"
							/>
						</svg>
					</button>
				</div>
			</div>
		{:else if isInCall}
			<!-- Active call UI -->
			<div class="flex flex-col items-center space-y-6">
				<div class="h-24 w-24 overflow-hidden rounded-full bg-black">
					<div class="flex h-full w-full items-center justify-center text-4xl text-white">
						{remotePeer?.[0]?.toUpperCase() || incomingCallData?.from?.[0]?.toUpperCase() || '?'}
					</div>
				</div>
				<div class="text-center">
					<h3 class="text-xl font-semibold">
						{remotePeer || incomingCallData?.from || 'Calling...'}
					</h3>
					<p class="mt-1 text-sm text-gray-500">{currentCallStatus}</p>
					{#if currentCallStatus === 'Connected'}
						<p class="mt-2 font-mono text-lg">{formatDuration(callDuration)}</p>
					{/if}
				</div>
				<div class="flex justify-center">
					<button
						on:click={endCall}
						class="flex h-16 w-16 items-center justify-center rounded-full bg-red-500 text-white transition-colors hover:bg-red-600"
						aria-label="End call"
					>
						<svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M6 18L18 6M6 6l12 12"
							/>
						</svg>
					</button>
				</div>
			</div>
		{:else}
			<!-- Initial outgoing call UI -->
			<div class="flex flex-col items-center space-y-6">
				<div class="h-24 w-24 overflow-hidden rounded-full bg-black">
					<div class="flex h-full w-full items-center justify-center text-4xl text-white">
						{remotePeer?.[0]?.toUpperCase() || '?'}
					</div>
				</div>
				<div class="text-center">
					<h3 class="text-xl font-semibold">{remotePeer || 'Unknown'}</h3>
					<p class="mt-1 text-sm text-gray-500">{currentCallStatus}</p>
				</div>
				<div class="flex items-center space-x-6">
					<button
						on:click={initiateCall}
						class="flex h-16 w-16 items-center justify-center rounded-full bg-green-500 text-white transition-colors hover:bg-green-600"
						disabled={!remotePeer}
						aria-label="Start call"
					>
						<svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
							/>
						</svg>
					</button>
					<button
						on:click={cancelCall}
						class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-200 text-gray-600 transition-colors hover:bg-gray-300"
						aria-label="Cancel"
					>
						<svg class="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M6 18L18 6M6 6l12 12"
							/>
						</svg>
					</button>
				</div>
			</div>
		{/if}
	</div>
</div>
