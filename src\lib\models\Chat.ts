import mongoose, { Document, Schema } from 'mongoose';
import { encrypt, decrypt } from '$lib/utils/encryption';

interface IChat extends Document {
	sender: mongoose.Schema.Types.ObjectId;
	receiver?: mongoose.Schema.Types.ObjectId;
	group?: mongoose.Schema.Types.ObjectId;
	message: string;
	timestamp: Date;
}

const ChatSchema = new Schema<IChat>({
	sender: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
		required: true,
		index: true
	},
	receiver: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'User',
		index: true
	},
	group: {
		type: mongoose.Schema.Types.ObjectId,
		ref: 'Group',
		index: true
	},
	message: {
		type: String,
		required: true,
		set: function (message: string) {
			try {
				return encrypt(message);
			} catch (error) {
				console.error('Error encrypting message:', error);
				return message;
			}
		},
		get: function (message: string) {
			try {
				return decrypt(message);
			} catch (error) {
				console.error('Error decrypting message:', error);
				return '[Encrypted message]';
			}
		}
	},
	timestamp: { type: Date, default: Date.now }
});

// Make sure mongoose uses the getters
ChatSchema.set('toJSON', { getters: true });
ChatSchema.set('toObject', { getters: true });

// Add validation to ensure either receiver or group is present
ChatSchema.pre('save', function (next) {
	if (!this.receiver && !this.group) {
		next(new Error('Message must have either a receiver or a group'));
	} else if (this.receiver && this.group) {
		next(new Error('Message cannot have both receiver and group'));
	}
	next();
});

const Chat = mongoose.models.Chat || mongoose.model<IChat>('Chat', ChatSchema);

export default Chat;
