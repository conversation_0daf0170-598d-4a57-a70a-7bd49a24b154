export class CallService {
	private peerConnection: RTCPeerConnection;
	private localStream: MediaStream | null = null;
	private signalingService: SignalingService;
	private onCallStatus: ((status: string) => void) | null = null;
	private onIncomingCall: ((from: string, offer: RTCSessionDescription) => void) | null = null;
	private remotePeer: string | null = null;
	private iceCandidatesQueue: RTCIceCandidate[] = [];
	private remoteAudioElement: HTMLAudioElement | null = null;

	constructor(signalingService: SignalingService) {
		this.signalingService = signalingService;
		this.initializePeerConnection();

		this.signalingService.setCallHandler(async (from, offer) => {
			console.log('Received call from:', from);
			this.remotePeer = from;
			if (this.onIncomingCall) {
				this.onIncomingCall(from, offer);
			}
		});

		this.signalingService.setIceCandidateHandler((candidate) => {
			console.log('Received ICE candidate');
			if (this.peerConnection.remoteDescription) {
				this.peerConnection
					.addIceCandidate(candidate)
					.catch((err) => console.error('Error adding received ice candidate', err));
			} else {
				this.iceCandidatesQueue.push(candidate);
			}
		});

		this.signalingService.setAnswerHandler((answer) => {
			console.log('Received answer');
			this.peerConnection
				.setRemoteDescription(answer)
				.then(() => {
					// Process any queued ICE candidates
					while (this.iceCandidatesQueue.length > 0) {
						const candidate = this.iceCandidatesQueue.shift();
						if (candidate) {
							this.peerConnection
								.addIceCandidate(candidate)
								.catch((err) => console.error('Error adding queued ice candidate', err));
						}
					}
					this.updateCallStatus('Connected');
				})
				.catch((err) => console.error('Error setting remote description', err));
		});
	}

	private initializePeerConnection() {
		this.peerConnection = new RTCPeerConnection({
			iceServers: [
				{ urls: 'stun:stun.l.google.com:19302' },
				{ urls: 'stun:stun1.l.google.com:19302' }
			]
		});

		this.peerConnection.onicecandidate = (event) => {
			if (event.candidate && this.remotePeer) {
				this.signalingService.sendIceCandidate(this.remotePeer, event.candidate);
			}
		};

		this.peerConnection.onconnectionstatechange = () => {
			console.log('Connection state:', this.peerConnection.connectionState);
			if (this.peerConnection.connectionState === 'connected') {
				this.updateCallStatus('Connected');
			} else if (
				this.peerConnection.connectionState === 'failed' ||
				this.peerConnection.connectionState === 'disconnected' ||
				this.peerConnection.connectionState === 'closed'
			) {
				this.updateCallStatus('Call ended');
			}
		};

		this.peerConnection.ontrack = (event) => {
			console.log('Received remote track');
			if (event.streams && event.streams[0]) {
				if (this.remoteAudioElement) {
					this.remoteAudioElement.remove();
				}

				this.remoteAudioElement = document.createElement('audio');
				this.remoteAudioElement.srcObject = event.streams[0];
				this.remoteAudioElement.autoplay = true;
				this.remoteAudioElement.style.display = 'none';
				document.body.appendChild(this.remoteAudioElement);

				this.updateCallStatus('Connected');
			}
		};
	}

	public destroy() {
		this.endCall(); // Ensure call is properly ended
		// Clean up all event listeners
		this.peerConnection.onicecandidate = null;
		this.peerConnection.onconnectionstatechange = null;
		this.peerConnection.ontrack = null;
		this.onCallStatus = null;
		this.onIncomingCall = null;

		// Remove audio elements
		if (this.remoteAudioElement) {
			this.remoteAudioElement.remove();
			this.remoteAudioElement = null;
		}

		// Stop local stream
		if (this.localStream) {
			this.localStream.getTracks().forEach((track) => track.stop());
			this.localStream = null;
		}
	}

	private updateCallStatus(status: string) {
		if (this.onCallStatus) {
			this.onCallStatus(status);
		}
	}

	setCallStatusHandler(handler: (status: string) => void) {
		this.onCallStatus = handler;
	}

	setIncomingCallHandler(handler: (from: string, offer: RTCSessionDescription) => void) {
		this.onIncomingCall = handler;
	}

	async startCall(remotePeer: string) {
		this.remotePeer = remotePeer;
		this.updateCallStatus('Calling...');

		try {
			this.localStream = await navigator.mediaDevices.getUserMedia({
				audio: true
			});

			this.localStream.getTracks().forEach((track) => {
				if (this.localStream) {
					this.peerConnection.addTrack(track, this.localStream);
				}
			});

			const offer = await this.peerConnection.createOffer();
			await this.peerConnection.setLocalDescription(offer);

			this.signalingService.sendCallRequest(remotePeer, offer);
		} catch (error) {
			console.error('Error starting call:', error);
			this.updateCallStatus('Failed to start call');
		}
	}

	async handleIncomingCall(from: string, offer: RTCSessionDescription) {
		try {
			this.remotePeer = from;
			this.updateCallStatus('Connecting...');

			await this.peerConnection.setRemoteDescription(new RTCSessionDescription(offer));

			this.localStream = await navigator.mediaDevices.getUserMedia({
				audio: true
			});

			this.localStream.getTracks().forEach((track) => {
				if (this.localStream) {
					this.peerConnection.addTrack(track, this.localStream);
				}
			});

			const answer = await this.peerConnection.createAnswer();
			await this.peerConnection.setLocalDescription(answer);

			this.signalingService.sendAnswer(from, answer);

			// Process any queued ICE candidates
			while (this.iceCandidatesQueue.length > 0) {
				const candidate = this.iceCandidatesQueue.shift();
				if (candidate) {
					await this.peerConnection
						.addIceCandidate(candidate)
						.catch((err) => console.error('Error adding queued ice candidate', err));
				}
			}
		} catch (error) {
			console.error('Error handling incoming call:', error);
			this.updateCallStatus('Failed to connect');
		}
	}

	endCall() {
		try {
			if (this.localStream) {
				this.localStream.getTracks().forEach((track) => track.stop());
				this.localStream = null;
			}

			this.peerConnection.close();

			if (this.remoteAudioElement) {
				this.remoteAudioElement.remove();
				this.remoteAudioElement = null;
			}

			document.querySelectorAll('audio').forEach((el) => {
				if (!el.controls) {
					el.remove();
				}
			});

			setTimeout(() => {
				this.initializePeerConnection();
				this.iceCandidatesQueue = [];
				this.remotePeer = null;
				this.updateCallStatus('Call ended');
			}, 100);
		} catch (error) {
			console.error('Error ending call:', error);
		}
	}

	rejectCall() {
		this.signalingService.sendCallRejection(this.remotePeer);
		this.remotePeer = null;
	}

	private setupAudio() {
		const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

		// Configure audio to ensure it works correctly on mobile
		this.peerConnection.ontrack = (event) => {
			console.log('Received remote track');
			if (event.streams && event.streams[0]) {
				if (this.remoteAudioElement) {
					this.remoteAudioElement.remove();
				}

				this.remoteAudioElement = document.createElement('audio');
				this.remoteAudioElement.srcObject = event.streams[0];
				this.remoteAudioElement.autoplay = true;
				this.remoteAudioElement.setAttribute('playsinline', '');
				this.remoteAudioElement.style.display = 'none';
				document.body.appendChild(this.remoteAudioElement);

				// Enable speaker audio
				this.remoteAudioElement
					.play()
					.then(() => {
						console.log('Remote audio playing');
						this.updateCallStatus('Connected');
					})
					.catch((err) => {
						console.error('Error playing remote audio:', err);
						this.updateCallStatus('Audio playback error');
					});
			}
		};
	}
}
