<script lang="ts">
	import { auth } from '$lib/stores/auth';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	let messages: { sender: string; message: string }[] = [];
	let newMessage = '';
	let currentUser = '';

	auth.subscribe((state) => {
		currentUser = state.username || '';
	});

	const fetchMessages = async () => {
		if (!currentUser) return;

		const res = await fetch(`/api/chat/messages?receiver=${data.receiver}`, {
			credentials: 'include'
		});
		if (res.ok) {
			messages = await res.json();
		}
	};

	const sendMessage = async () => {
		if (!newMessage || !currentUser) return;

		await fetch('/api/chat/send', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			credentials: 'include',
			body: JSON.stringify({
				receiver: data.receiver,
				message: newMessage
			})
		});
		newMessage = '';
		await fetchMessages();
	};

	onMount(() => {
		if (currentUser) {
			fetchMessages();
		}
	});
</script>

<div class="min-h-screen bg-slate-50 p-4">
	<h1 class="mb-4 font-mono text-2xl">Chat with {data.receiver}</h1>
	<div class="mb-4 space-y-2">
		{#each messages as { sender, message }}
			<div
				class={`rounded-lg p-2 ${sender === currentUser ? 'ml-auto bg-black text-white' : 'bg-gray-100'} max-w-[70%]`}
			>
				<p class="text-sm">{message}</p>
				<p class="text-xs opacity-70">{sender}</p>
			</div>
		{/each}
	</div>
	<div class="flex gap-2">
		<input
			bind:value={newMessage}
			placeholder="Type a message..."
			class="flex-1 rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
		/>
		<button
			on:click={sendMessage}
			class="rounded-md bg-black px-4 py-2 font-mono text-sm text-white transition-colors hover:bg-gray-800"
		>
			Send
		</button>
	</div>
</div>
