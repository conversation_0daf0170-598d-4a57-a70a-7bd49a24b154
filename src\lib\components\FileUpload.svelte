<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	export let multiple = false;
	export let accept = '*/*';
	export let maxSize = 5 * 1024 * 1024; // 5MB default
	export let disabled = false;

	const dispatch = createEventDispatcher<{
		filesSelected: { files: File[] };
		error: { message: string };
	}>();

	let fileInput: HTMLInputElement;
	let dragOver = false;

	function handleFileSelect(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files) {
			processFiles(Array.from(input.files));
		}
	}

	function handleDrop(event: DragEvent) {
		event.preventDefault();
		dragOver = false;
		
		if (disabled) return;
		
		const files = event.dataTransfer?.files;
		if (files) {
			processFiles(Array.from(files));
		}
	}

	function handleDragOver(event: DragEvent) {
		event.preventDefault();
		if (!disabled) {
			dragOver = true;
		}
	}

	function handleDragLeave(event: DragEvent) {
		event.preventDefault();
		dragOver = false;
	}

	function processFiles(files: File[]) {
		const validFiles: File[] = [];
		
		for (const file of files) {
			if (file.size > maxSize) {
				dispatch('error', { message: `File "${file.name}" is too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB.` });
				continue;
			}
			validFiles.push(file);
		}

		if (validFiles.length > 0) {
			dispatch('filesSelected', { files: validFiles });
		}

		// Clear the input
		if (fileInput) {
			fileInput.value = '';
		}
	}

	function openFileDialog() {
		if (!disabled) {
			fileInput?.click();
		}
	}
</script>

<input
	bind:this={fileInput}
	type="file"
	{accept}
	{multiple}
	{disabled}
	on:change={handleFileSelect}
	class="hidden"
/>

<div
	class="relative cursor-pointer transition-colors duration-200 {dragOver ? 'bg-blue-50 border-blue-300' : 'hover:bg-gray-50'} {disabled ? 'opacity-50 cursor-not-allowed' : ''}"
	on:click={openFileDialog}
	on:drop={handleDrop}
	on:dragover={handleDragOver}
	on:dragleave={handleDragLeave}
	role="button"
	tabindex="0"
	on:keydown={(e) => {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			openFileDialog();
		}
	}}
>
	<slot {dragOver} {disabled} />
</div>
