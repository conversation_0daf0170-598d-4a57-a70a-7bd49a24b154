import mongoose, { Document, Schema } from 'mongoose';

interface IGroup extends Document {
	name: string;
	members: mongoose.Schema.Types.ObjectId[];
	createdBy: mongoose.Schema.Types.ObjectId;
	timestamp: Date;
}

const GroupSchema = new Schema<IGroup>({
	name: { type: String, required: true },
	members: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
	createdBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
	timestamp: { type: Date, default: Date.now }
});

const Group = mongoose.models.Group || mongoose.model<IGroup>('Group', GroupSchema);

export default Group;
