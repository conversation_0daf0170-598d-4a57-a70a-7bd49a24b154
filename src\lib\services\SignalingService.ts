import { formatDistanceToNow } from 'date-fns'; // Import date-fns

export class SignalingService {
	private socket: WebSocket;
	private onCallReceived: ((from: string, offer: RTCSessionDescription) => void) | null = null;
	private onIceCandidateReceived: ((candidate: RTCIceCandidate) => void) | null = null;
	private onAnswerReceived: ((answer: RTCSessionDescription) => void) | null = null;
	private onCallRejected: (() => void) | null = null;
	private username: string;
	private reconnectAttempts = 0;
	private maxReconnectAttempts = 5;
	private reconnectTimeout: ReturnType<typeof setTimeout> | null = null;
	private messageQueue: any[] = [];
	private static connections = new Map<string, SignalingService>();
	private onUserTyping: ((user: string, isGroup: boolean, targetId: string) => void) | null = null;
	private onUserStoppedTyping: ((user: string, isGroup: boolean, targetId: string) => void) | null =
		null;
	private onMessageDeleted:
		| ((messageId: string, targetId: string, isGroup: boolean) => void)
		| null = null;
	private onGroupDeleted: ((groupId: string) => void) | null = null;
	private typingTimeout: ReturnType<typeof setTimeout> | null = null; // Timer for stopped typing

	constructor(username: string) {
		if (SignalingService.connections.has(username)) {
			console.log(`Reusing existing signaling service for ${username}`);
			return SignalingService.connections.get(username)!;
		}

		this.username = username;
		this.initSocket();
		SignalingService.connections.set(username, this);
	}

	private initSocket() {
		this.socket = new WebSocket(`ws://localhost:6969?username=${this.username}`);

		this.socket.onopen = () => {
			console.log('Connected to signaling server');
			this.reconnectAttempts = 0;

			// Process any queued messages
			while (this.messageQueue.length > 0) {
				const message = this.messageQueue.shift();
				if (message) {
					this.socket.send(JSON.stringify(message));
				}
			}
		};

		this.socket.onmessage = (event) => {
			try {
				const data = JSON.parse(event.data);
				console.log('Received WS message:', data.type, data);

				switch (data.type) {
					case 'call-request':
						if (this.onCallReceived) {
							console.log('Received call request from:', data.from);
							this.onCallReceived(data.from, data.offer);
						}
						break;
					case 'ice-candidate':
						if (this.onIceCandidateReceived && data.candidate) {
							this.onIceCandidateReceived(new RTCIceCandidate(data.candidate));
						}
						break;
					case 'answer':
						if (this.onAnswerReceived && data.answer) {
							this.onAnswerReceived(new RTCSessionDescription(data.answer));
						}
						break;
					case 'call-rejected':
						if (this.onCallRejected) {
							this.onCallRejected();
						}
						break;
					case 'typing':
						if (this.onUserTyping) {
							this.onUserTyping(data.from, data.isGroup, data.isGroup ? data.groupId : data.to);
						}
						break;
					case 'stopped-typing':
						if (this.onUserStoppedTyping) {
							this.onUserStoppedTyping(
								data.from,
								data.isGroup,
								data.isGroup ? data.groupId : data.to
							);
						}
						break;
					case 'message-deleted':
						if (this.onMessageDeleted) {
							this.onMessageDeleted(data.messageId, data.groupId || data.from, !!data.groupId);
						}
						break;
					case 'group-deleted':
						if (this.onGroupDeleted) {
							this.onGroupDeleted(data.groupId);
						}
						break;
				}
			} catch (error) {
				console.error('Error parsing WebSocket message:', error);
			}
		};

		this.socket.onerror = (error) => {
			console.error('WebSocket error:', error);
		};

		this.socket.onclose = (event) => {
			console.log('WebSocket closed with code:', event.code);
			this.handleReconnect();
		};
	}

	private handleReconnect() {
		if (this.reconnectAttempts < this.maxReconnectAttempts) {
			this.reconnectAttempts++;
			const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

			console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

			if (this.reconnectTimeout) {
				clearTimeout(this.reconnectTimeout);
			}

			this.reconnectTimeout = setTimeout(() => {
				this.initSocket();
			}, delay);
		} else {
			console.error('Max reconnection attempts reached, giving up');
		}
	}

	setCallHandler(handler: (from: string, offer: RTCSessionDescription) => void) {
		this.onCallReceived = handler;
	}

	setIceCandidateHandler(handler: (candidate: RTCIceCandidate) => void) {
		this.onIceCandidateReceived = handler;
	}

	setAnswerHandler(handler: (answer: RTCSessionDescription) => void) {
		this.onAnswerReceived = handler;
	}

	setCallRejectedHandler(handler: () => void) {
		this.onCallRejected = handler;
	}

	sendCallRequest(to: string, offer: RTCSessionDescription) {
		this.sendMessage({
			type: 'call-request',
			to,
			offer
		});
	}

	sendIceCandidate(to: string, candidate: RTCIceCandidate) {
		this.sendMessage({
			type: 'ice-candidate',
			to,
			candidate
		});
	}

	sendAnswer(to: string, answer: RTCSessionDescription) {
		this.sendMessage({
			type: 'answer',
			to,
			answer
		});
	}

	sendCallRejection(to: string | null) {
		if (!to) return;

		this.sendMessage({
			type: 'call-rejected',
			to
		});
	}

	private sendMessage(data: any) {
		if (this.socket.readyState === WebSocket.OPEN) {
			this.socket.send(JSON.stringify(data));
		} else {
			console.log('WebSocket not open, queueing message');
			this.messageQueue.push(data);

			if (
				this.socket.readyState === WebSocket.CLOSED ||
				this.socket.readyState === WebSocket.CLOSING
			) {
				this.handleReconnect();
			}
		}
	}
	setTypingHandlers(
		onTyping: (user: string, isGroup: boolean, targetId: string) => void,
		onStoppedTyping: (user: string, isGroup: boolean, targetId: string) => void
	) {
		this.onUserTyping = onTyping;
		this.onUserStoppedTyping = onStoppedTyping;
	}

	setMessageDeletedHandler(
		handler: (messageId: string, targetId: string, isGroup: boolean) => void
	) {
		this.onMessageDeleted = handler;
	}

	setGroupDeletedHandler(handler: (groupId: string) => void) {
		this.onGroupDeleted = handler;
	}
	sendTyping(to: string, isGroup: boolean, targetId: string, members: string[]) {
		if (this.typingTimeout) clearTimeout(this.typingTimeout);

		this.sendMessage({
			type: 'typing',
			to: isGroup ? null : to, // Target user or null for group
			isGroup,
			groupId: isGroup ? targetId : null,
			members: isGroup ? members : null // Include members for group broadcast
		});

		// Set a timeout to automatically send "stopped-typing"
		this.typingTimeout = setTimeout(() => {
			this.sendStoppedTyping(to, isGroup, targetId, members);
		}, 3000); // Adjust timeout duration as needed (e.g., 3 seconds)
	}

	sendStoppedTyping(to: string, isGroup: boolean, targetId: string, members: string[]) {
		if (this.typingTimeout) {
			clearTimeout(this.typingTimeout);
			this.typingTimeout = null;
		}
		this.sendMessage({
			type: 'stopped-typing',
			to: isGroup ? null : to,
			isGroup,
			groupId: isGroup ? targetId : null,
			members: isGroup ? members : null
		});
	}

	// Send requests to the WS server to broadcast deletion events
	broadcastMessageDeleted(
		messageId: string,
		conversationTarget: string,
		isGroup: boolean,
		members: string[]
	) {
		this.sendMessage({
			type: 'broadcast-delete-message',
			messageId,
			conversationTarget, // Username or Group ID
			isGroup,
			members: isGroup ? members : null // Required for group broadcast
		});
	}

	broadcastGroupDeleted(groupId: string, memberUsernames: string[]) {
		this.sendMessage({
			type: 'broadcast-delete-group',
			groupId,
			memberUsernames
		});
	}
	public close() {
		if (this.socket) {
			this.socket.onopen = null;
			this.socket.onmessage = null;
			this.socket.onclose = null;
			this.socket.onerror = null;

			if (this.socket.readyState === WebSocket.OPEN) {
				this.socket.close();
			}

			if (this.reconnectTimeout) {
				clearTimeout(this.reconnectTimeout);
				this.reconnectTimeout = null;
			}
		}

		SignalingService.connections.delete(this.username);
	}
	public static cleanup() {
		for (const service of SignalingService.connections.values()) {
			service.close();
		}
		SignalingService.connections.clear();
	}
}
