import mongoose from 'mongoose';

let isConnected = false;

export async function connect() {
	const mongoURI = 'mongodb://127.0.0.1:27017/konekt';

	// If already connected, return early
	if (isConnected && mongoose.connection.readyState === 1) {
		return;
	}

	try {
		// Close any existing connections
		if (mongoose.connection.readyState !== 0) {
			await mongoose.disconnect();
		}

		await mongoose.connect(mongoURI, {
			connectTimeoutMS: 10000, // Increased timeout
			socketTimeoutMS: 45000,
			maxPoolSize: 10, // Limit connection pool size
			serverSelectionTimeoutMS: 5000,
			heartbeatFrequencyMS: 10000
		});

		// Set mongoose-specific options
		mongoose.set('bufferCommands', false);
		mongoose.set('bufferMaxEntries', 0);

		isConnected = true;
		console.log('MongoDB connected successfully');

		// Handle connection events
		mongoose.connection.on('error', (error) => {
			console.error('MongoDB connection error:', error);
			isConnected = false;
		});

		mongoose.connection.on('disconnected', () => {
			console.log('MongoDB disconnected');
			isConnected = false;
		});

	} catch (error) {
		console.error('MongoDB connection error:', error);
		isConnected = false;
		throw error;
	}
}
