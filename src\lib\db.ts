import mongoose from 'mongoose';

export async function connect() {
	const mongoURI = 'mongodb://127.0.0.1:27017/konekt';

	try {
		if (mongoose.connection.readyState === 0) {
			await mongoose.connect(mongoURI, {
				connectTimeoutMS: 5000, // Timeout after 5 seconds
				socketTimeoutMS: 45000 // Close sockets after 45 seconds of inactivity
			});
			console.log('MongoDB connected successfully');
		}
	} catch (error) {
		console.error('MongoDB connection error:', error);
		throw error; // Rethrow to handle it in the calling function
	}
}
