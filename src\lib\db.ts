import mongoose from 'mongoose';

let isConnected = false;

export async function connect() {
	const mongoURI = 'mongodb://127.0.0.1:27017/konekt';

	// If already connected, return early
	if (isConnected && mongoose.connection.readyState === 1) {
		return;
	}

	try {
		await mongoose.connect(mongoURI);
		isConnected = true;
		console.log('MongoDB connected successfully');

		// Handle connection events
		mongoose.connection.on('error', (error) => {
			console.error('MongoDB connection error:', error);
			isConnected = false;
		});

		mongoose.connection.on('disconnected', () => {
			console.log('MongoDB disconnected');
			isConnected = false;
		});

	} catch (error) {
		console.error('MongoDB connection error:', error);
		isConnected = false;
		throw error;
	}
}
