# Cloudflare R2 Setup Guide for Konekt

This guide will help you set up Cloudflare R2 storage for avatar uploads and message attachments in your Konekt chat application.

## Prerequisites

1. A Cloudflare account
2. Access to Cloudflare R2 (may require payment method on file)

## Step 1: Create an R2 Bucket

1. Log in to your Cloudflare dashboard
2. Navigate to **R2 Object Storage** in the sidebar
3. Click **Create bucket**
4. Choose a bucket name (e.g., `konekt-storage` or `konekt-dev`)
5. Select a region close to your users
6. Click **Create bucket**

## Step 2: Configure Public Access (Optional)

If you want files to be publicly accessible via direct URLs:

1. Go to your bucket settings
2. Navigate to **Settings** > **Public access**
3. Click **Allow Access** and confirm
4. Note the public URL format: `https://your-bucket.your-account-id.r2.cloudflarestorage.com`

## Step 3: Create API Tokens

1. Go to **Manage Account** > **API Tokens**
2. Click **Create Token**
3. Use the **Custom token** template
4. Configure the token:
   - **Token name**: `Konekt R2 Access`
   - **Permissions**: 
     - `Cloudflare R2:Edit` for your account
   - **Account Resources**: Include your account
   - **Zone Resources**: Not needed
5. Click **Continue to summary** and **Create Token**
6. **IMPORTANT**: Copy the token immediately - you won't see it again!

## Step 4: Get Your Account ID

1. In your Cloudflare dashboard, look for your **Account ID** in the right sidebar
2. Copy this value

## Step 5: Configure Environment Variables

Create a `.env` file in your project root with the following:

```env
# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your_cloudflare_account_id_here
R2_ACCESS_KEY_ID=your_r2_access_key_here
R2_SECRET_ACCESS_KEY=your_r2_secret_key_here
R2_BUCKET_NAME=your_bucket_name_here
R2_PUBLIC_URL=https://your-bucket.your-account-id.r2.cloudflarestorage.com

# JWT Secret (change this!)
JWT_SECRET=your-super-secret-jwt-key-here

# MongoDB
MONGODB_URI=mongodb://127.0.0.1:27017/konekt
```

## Step 6: Update R2 Service Configuration

Edit `src/lib/services/r2Storage.ts` and update the development configuration:

```typescript
// Replace the dev configuration with your actual values
const R2_ACCOUNT_ID = dev ? 'your_actual_account_id' : process.env.R2_ACCOUNT_ID;
const R2_ACCESS_KEY_ID = dev ? 'your_actual_access_key' : process.env.R2_ACCESS_KEY_ID;
const R2_SECRET_ACCESS_KEY = dev ? 'your_actual_secret_key' : process.env.R2_SECRET_ACCESS_KEY;
const R2_BUCKET_NAME = dev ? 'your-dev-bucket-name' : process.env.R2_BUCKET_NAME || 'konekt';
const R2_PUBLIC_URL = dev ? 'https://your-dev-bucket.your-account-id.r2.cloudflarestorage.com' : process.env.R2_PUBLIC_URL;
```

## Features Implemented

### ✅ Avatar Uploads
- Upload profile pictures up to 5MB
- Supports JPEG, PNG, GIF, WebP formats
- Automatic old avatar deletion when updating
- Accessible via Settings page

### ✅ Message Attachments
- Upload files up to 5MB each
- Supports images, documents, archives
- Drag & drop file upload
- File preview in chat
- Download functionality

### ✅ File Types Supported
- **Images**: JPEG, PNG, GIF, WebP, SVG
- **Documents**: PDF, TXT, DOC, DOCX
- **Archives**: ZIP, RAR, 7Z
- **Data**: JSON, CSV

## Testing the Setup

1. Start your development server: `npm run dev`
2. Go to `/settings` to test avatar upload
3. Go to `/chat` and try uploading files using the attachment button
4. Check your R2 bucket to see uploaded files

## Troubleshooting

### Common Issues:

1. **"Access Denied" errors**: Check your API token permissions
2. **"Bucket not found"**: Verify bucket name and account ID
3. **CORS errors**: Configure CORS in your R2 bucket settings if needed
4. **File not uploading**: Check file size (max 5MB) and file type

### Debug Mode:

Check browser console and server logs for detailed error messages.

## Security Notes

- Never commit your `.env` file to version control
- Use different buckets for development and production
- Consider implementing user upload quotas
- Regularly review and rotate API tokens

## Cost Considerations

Cloudflare R2 pricing (as of 2024):
- Storage: $0.015/GB/month
- Class A operations (writes): $4.50/million
- Class B operations (reads): $0.36/million
- No egress fees!

For a small chat app, costs should be minimal.
