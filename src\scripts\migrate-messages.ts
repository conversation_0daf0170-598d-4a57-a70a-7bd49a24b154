import { connect } from '../lib/db';
import Chat from '../lib/models/Chat';
import { encrypt } from '../lib/utils/encryption';

async function migrateMessages() {
	try {
		await connect();

		const messages = await Chat.find({
			message: { $not: /^encrypted:/ } // Find messages that aren't encrypted
		});

		console.log(`Found ${messages.length} messages to migrate`);

		for (const message of messages) {
			const encrypted = encrypt(message.message);
			await Chat.updateOne({ _id: message._id }, { $set: { message: encrypted } });
		}

		console.log('Migration completed successfully');
		process.exit(0);
	} catch (error) {
		console.error('Migration failed:', error);
		process.exit(1);
	}
}

migrateMessages();
