<script lang="ts">
	import { goto } from '$app/navigation';

	let username = '';
	let password = '';
	let error = '';
	let success = '';

	const register = async () => {
		const res = await fetch('/api/auth/register', {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({ username, password })
		});
		const data = await res.json();

		if (res.ok) {
			success = 'Registration successful! Redirecting to login...';
			error = '';
			setTimeout(() => goto('/login'), 2000);
		} else {
			error = data.error;
			success = '';
		}
	};
</script>

<div class="min-h-screen bg-slate-50">
	<div class="container mx-auto px-4 py-8">
		<nav class="mb-8 flex items-center justify-between">
			<a href="/" class="font-mono text-xl text-black">konekt</a>
			<div class="flex items-center space-x-6">
				<a href="/" class="font-mono text-sm text-black hover:text-gray-600">home</a>
				<a href="/login" class="font-mono text-sm text-black hover:text-gray-600">login</a>
				<a href="/register" class="font-mono text-sm text-black hover:text-gray-600">register</a>
			</div>
		</nav>

		<div class="mx-auto max-w-md">
			<div class="rounded-lg bg-white p-8 shadow-lg">
				<h1 class="mb-6 font-mono text-2xl text-black">register</h1>

				<div class="space-y-4">
					<div>
						<input
							bind:value={username}
							placeholder="username"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
					<div>
						<input
							bind:value={password}
							type="password"
							placeholder="password"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
					{#if error}
						<p class="text-sm text-red-500">{error}</p>
					{/if}
					{#if success}
						<p class="text-sm text-green-500">{success}</p>
					{/if}
					<button
						on:click={register}
						class="w-full rounded-md bg-black px-4 py-2 font-mono text-sm text-white transition-colors hover:bg-gray-800"
					>
						register
					</button>
				</div>
			</div>
		</div>
	</div>
</div>
