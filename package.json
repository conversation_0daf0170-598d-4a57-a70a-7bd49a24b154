{"name": "konekt", "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "build:migrate": "node src/scripts/build-migrate.js", "migrate": "node dist/migrate-messages.js"}, "devDependencies": {"@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@types/bcryptjs": "^2.4.6", "@types/eslint": "^9.6.0", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.9.0", "autoprefixer": "^10.4.20", "date-fns": "^4.1.0", "eslint": "^9.7.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "globals": "^15.0.0", "prettier": "^3.3.2", "prettier-plugin-svelte": "^3.2.6", "prettier-plugin-tailwindcss": "^0.6.5", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.4.9", "typescript": "^5.6.3", "typescript-eslint": "^8.0.0", "vite": "^5.0.3"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "bcryptjs": "^2.4.3", "cookie": "^1.0.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.8.1", "multer": "^2.0.0"}}